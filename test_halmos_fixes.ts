import { halmosLogsToFunctions } from "./src/halmos/index";

// Test case 1: Missing parameter issue from user's example
const testLogs1 = `
[FAIL] invariant_amt_isAbove0() (paths: 1, time: 0.12s, bounds: [])
Counterexample:
p_entropy_uint256_af384ae_74 = 0x00
Sequence:
    CALL CryticToFoundry::switchActor(p_entropy_uint256_af384ae_74)
    CALL CryticToFoundry::invariant_amt_isAbove0()
`;

// Test case 2: Missing parameter in setIsManager call
const testLogs2 = `
[FAIL] invariant_isNeverManager() (paths: 1, time: 0.12s, bounds: [])
Counterexample:
p_entropy_uint256 = 0x00
p_isManager_bool = 0x01
p_manager_address = 0x0000000000000000000000000000000000000000
Sequence:
    CALL CryticToFoundry::switchActor(p_entropy_uint256)
    CALL CryticToFoundry::setIsManager(p_manager_address, p_isManager_bool)
    CALL CryticToFoundry::invariant_isNeverManager()
`;

// Test case 3: Log format from the gist (different format)
const testLogs3 = `
Assertion failure detected in CryticToFoundry.doomsday_increment_never_reverts()
Counterexample: 
    halmos_block_timestamp_depth1_2c253a9 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6246134_23 = 0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6442b8f_55 = 0x00
    p_newNumber_uint256_566942c_24 = 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
Sequence:
    CALL CryticToFoundry::counter_setNumber1(p_newNumber_uint256_566942c_24) (value: halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6246134_23) 
(caller: halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_f96fd46_22)
    CALL CryticToFoundry::doomsday_increment_never_reverts() (value: halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6442b8f_55) (caller: 
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_d4d44af_54)
`;

console.log("=== Test 1: Missing parameter issue ===");
const result1 = halmosLogsToFunctions(testLogs1, "test");
console.log(result1);

console.log("\n=== Test 2: Missing parameter in setIsManager ===");
const result2 = halmosLogsToFunctions(testLogs2, "test");
console.log(result2);

console.log("\n=== Test 3: Different log format ===");
const result3 = halmosLogsToFunctions(testLogs3, "test");
console.log(result3);

// Check if the issues are fixed
console.log("\n=== Analysis ===");
console.log(
  "Test 1 - Should have entropy_uint256 parameter:",
  result1.includes("entropy_uint256")
);
console.log(
  "Test 2 - Should have all parameters:",
  result2.includes("entropy_uint256") &&
    result2.includes("isManager_bool") &&
    result2.includes("manager_address")
);
console.log(
  "Test 3 - Should recognize assertion failure format:",
  result3.includes("doomsday_increment_never_reverts")
);
